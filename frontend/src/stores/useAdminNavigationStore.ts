'use client';

import { create } from 'zustand';

interface AdminNavigationStore {
  postId: number;
  userId: number;
  isCollapsed: boolean;

  setActiveId: (type: 'post' | 'user', id: number) => void;
  toggleCollapse: () => void;
  reset: () => void;
}

export const useAdminNavigationStore = create<AdminNavigationStore>((set, get) => ({
  postId: 0,
  userId: 0,
  isCollapsed: false,

  setActiveId: (type, id) => {
    const key = `${type}Id` as 'postId' | 'userId';
    const currentValue = get()[key];

    if (currentValue !== id) {
      set({ [key]: id });
    }
  },

  toggleCollapse: () => {
    set((state) => ({ isCollapsed: !state.isCollapsed }));
  },

  reset: () => set({
    postId: 0,
    userId: 0,
    isCollapsed: false,
  })
}));
