/* .nc-MainNav {
  &.notOnTop {
    @apply bg-white dark:bg-neutral-900 lg:backdrop-blur-2xl lg:bg-opacity-70  shadow-sm dark:border-b dark:border-neutral-700;
  }
} */

.nc-MainNav1 {
  &.notOnTop {
    @apply bg-white dark:bg-neutral-900 backdrop-blur-2xl bg-opacity-70 dark:bg-opacity-60 shadow-sm dark:border-b dark:border-neutral-700;
  }
}

.menu-item.menu-megamenu:hover {
  > .sub-menu {
    @apply visible;
  }
}

/* NavigationAdmin specific styles */
.nc-NavigationAdmin-container {
  /* When collapsed, hide everything */
  &.collapsed {
    .nc-Navigation-admin {
      display: none;
    }
  }

  /* When expanded, ensure submenu can overflow the container */
  &:not(.collapsed) {
    overflow: visible;

    .nc-Navigation-admin {
      /* High z-index to ensure submenus appear above other elements */
      z-index: 50;

      /* Ensure menu items can show dropdowns */
      .menu-item {
        position: relative;

        /* Dropdown menu hover functionality */
        &.menu-dropdown:hover {
          > .sub-menu {
            @apply visible;
          }
        }

        /* Ensure Popover panels have proper z-index */
        .sub-menu {
          z-index: 100;
        }
      }
    }
  }

  /* Sticky show button styles */
  .sticky-show-button {
    /* Ensure it's above everything else */
    z-index: 9999;

    /* Smooth entrance animation */
    animation: slideInFromRight 0.3s ease-out;
  }
}

/* Animation for sticky button */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation for toolbar slide down */
@keyframes slideDownFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  60% {
    transform: translateY(10px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Toolbar slide down animation */
.toolbar-slide-down {
  animation: slideDownFromTop 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Add subtle shadow during animation */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  /* Toggle button animation */
  > div:first-child {
    animation: fadeInScale 0.5s ease-out 0.2s both;

    button {
      /* Add glow effect during entrance */
      animation: buttonGlow 0.8s ease-out 0.3s both;
    }
  }

  /* Navigation menu animation */
  > div:last-child {
    animation: fadeInUp 0.5s ease-out 0.3s both;

    /* Add shimmer effect to the separator line */
    .absolute {
      animation: shimmer 1s ease-out 0.5s both;
    }
  }

  /* Staggered animation for menu items */
  .nc-Navigation-admin li {
    animation: fadeInUp 0.4s ease-out both;

    &:nth-child(1) { animation-delay: 0.4s; }
    &:nth-child(2) { animation-delay: 0.5s; }
    &:nth-child(3) { animation-delay: 0.6s; }
    &:nth-child(4) { animation-delay: 0.7s; }
    &:nth-child(5) { animation-delay: 0.8s; }

    /* Add hover preparation */
    &:hover {
      transform: translateY(-2px);
      transition: transform 0.2s ease-out;
    }
  }

  /* Responsive animation adjustments */
  @media (max-width: 768px) {
    animation-duration: 0.4s; /* Faster on mobile */

    .nc-Navigation-admin li {
      animation-duration: 0.3s;

      &:nth-child(1) { animation-delay: 0.3s; }
      &:nth-child(2) { animation-delay: 0.35s; }
      &:nth-child(3) { animation-delay: 0.4s; }
      &:nth-child(4) { animation-delay: 0.45s; }
      &:nth-child(5) { animation-delay: 0.5s; }
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    animation: fadeIn 0.3s ease-out;

    > div:first-child,
    > div:last-child,
    .nc-Navigation-admin li {
      animation: fadeIn 0.2s ease-out;
      animation-delay: 0s !important;
    }
  }
}

/* Fade in up animation for menu items */
@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Fade in scale animation for toggle button */
@keyframes fadeInScale {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Button glow animation */
@keyframes buttonGlow {
  0% {
    box-shadow: 0 0 0 rgba(40, 129, 67, 0);
  }
  50% {
    box-shadow: 0 0 20px rgba(40, 129, 67, 0.3);
  }
  100% {
    box-shadow: 0 0 0 rgba(40, 129, 67, 0);
  }
}

/* Shimmer animation for separator line */
@keyframes shimmer {
  0% {
    opacity: 0;
    transform: translateX(-100%) scaleX(0);
  }
  50% {
    opacity: 1;
    transform: translateX(0) scaleX(1.2);
  }
  100% {
    opacity: 0.2;
    transform: translateX(0) scaleX(1);
  }
}

/* Simple fade in for reduced motion */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
