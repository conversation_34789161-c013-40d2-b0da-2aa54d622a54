'use client';

import React from 'react';
import NavigationAdmin from './NavigationAdmin';
import { INavItemType } from '@/contains/types';

// Test data với dropdown menu
const TEST_NAVIGATION_ADMIN: INavItemType[] = [
  {
    id: 'admin-home',
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: 'admin-menu',
    href: "",
    name: "<PERSON>u quản lý",
    type: "dropdown",
    children: [
      {
        id: 'admin-posts',
        href: "/admin/blog/posts",
        name: "Quản lý bài viết",
      },
      {
        id: 'admin-categories',
        href: "/admin/blog/categories",
        name: "Chỉnh sửa danh mục",
      },
      {
        id: 'admin-tags',
        href: "/admin/blog/tags",
        name: "Chỉnh sửa thẻ",
      },
    ],
  },
  {
    id: 'admin-settings',
    href: "",
    name: "Cài đặt",
    type: "dropdown",
    children: [
      {
        id: 'admin-theme',
        href: "/admin/theme",
        name: "<PERSON><PERSON><PERSON>",
      },
      {
        id: 'admin-users',
        href: "/admin/users",
        name: "Người dùng",
      },
    ],
  },
];

/**
 * Demo component cho NavigationAdmin với slide down animation
 */
const NavigationAdminAnimationDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* NavigationAdmin với slide down animation */}
      <NavigationAdmin
        menu={TEST_NAVIGATION_ADMIN}
        isAdmin={true}
        postId={123}
      />
      
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            NavigationAdmin với Slide Down Animation
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Toolbar sẽ trượt từ trên xuống với animation mượt mà khi hiển thị
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🎬 Animation Sequence
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>0.0s</strong>: Toolbar slide down từ trên với bounce effect</li>
                <li>• <strong>0.2s</strong>: Toggle button fade in với scale animation</li>
                <li>• <strong>0.3s</strong>: Navigation menu fade in từ dưới lên</li>
                <li>• <strong>0.4s+</strong>: Menu items staggered animation (50ms delay)</li>
                <li>• <strong>Total</strong>: ~0.8s smooth animation sequence</li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ⚙️ Animation Details
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Easing</strong>: cubic-bezier(0.68, -0.55, 0.265, 1.55)</li>
                <li>• <strong>Duration</strong>: 0.6s main + staggered delays</li>
                <li>• <strong>Bounce Effect</strong>: 10px overshoot at 60%</li>
                <li>• <strong>Staggered Items</strong>: 100ms delay between items</li>
                <li>• <strong>Performance</strong>: GPU-accelerated transforms</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
            🧪 Test Animation
          </h3>
          <div className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
            <p>1. <strong>Click "Ẩn menu"</strong> → Toolbar biến mất, sticky button xuất hiện</p>
            <p>2. <strong>Click sticky button</strong> → Toolbar slide down với animation</p>
            <p>3. <strong>Observe sequence</strong>: Toolbar → Toggle button → Menu → Items</p>
            <p>4. <strong>Repeat test</strong> để xem animation consistency</p>
          </div>
        </div>

        {/* Animation Breakdown */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-green-900 dark:text-green-100">
              Phase 1: Slide Down
            </h4>
            <div className="space-y-2 text-sm text-green-800 dark:text-green-200">
              <p>• translateY(-100%) → translateY(10px) → translateY(0)</p>
              <p>• Bounce effect tại 60% timeline</p>
              <p>• Duration: 0.6s</p>
            </div>
          </div>
          
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-yellow-900 dark:text-yellow-100">
              Phase 2: Button Scale
            </h4>
            <div className="space-y-2 text-sm text-yellow-800 dark:text-yellow-200">
              <p>• scale(0.8) → scale(1)</p>
              <p>• Delay: 0.2s</p>
              <p>• Duration: 0.5s</p>
            </div>
          </div>
          
          <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-purple-900 dark:text-purple-100">
              Phase 3: Items Stagger
            </h4>
            <div className="space-y-2 text-sm text-purple-800 dark:text-purple-200">
              <p>• translateY(20px) → translateY(0)</p>
              <p>• Staggered delays: 0.4s, 0.5s, 0.6s...</p>
              <p>• Duration: 0.4s each</p>
            </div>
          </div>
        </div>

        {/* Demo Content */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Demo Content Area
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Khi click vào sticky button, toolbar sẽ slide down từ trên xuống với animation mượt mà.
            Animation được thiết kế để tạo cảm giác natural và professional.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <div key={item} className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  Content Block {item}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
                  Sed do eiusmod tempor incididunt ut labore.
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Technical Notes */}
        <div className="mt-8 bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
            🔧 Technical Implementation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">CSS Keyframes</h4>
              <pre className="text-xs bg-gray-200 dark:bg-gray-700 p-3 rounded overflow-x-auto">
{`@keyframes slideDownFromTop {
  0% { transform: translateY(-100%); opacity: 0; }
  60% { transform: translateY(10px); opacity: 0.8; }
  100% { transform: translateY(0); opacity: 1; }
}`}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Animation Class</h4>
              <pre className="text-xs bg-gray-200 dark:bg-gray-700 p-3 rounded overflow-x-auto">
{`.toolbar-slide-down {
  animation: slideDownFromTop 0.6s 
    cubic-bezier(0.68, -0.55, 0.265, 1.55);
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationAdminAnimationDemo;
