'use client';

import React from 'react';
import NavigationAdmin from './NavigationAdmin';
import { INavItemType } from '@/contains/types';

// Test data với dropdown menu để test fixed fade animation
const TEST_NAVIGATION_ADMIN: INavItemType[] = [
  {
    id: 'admin-home',
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: 'admin-content',
    href: "",
    name: "<PERSON><PERSON> quản lý",
    type: "dropdown",
    children: [
      {
        id: 'admin-posts',
        href: "/admin/blog/posts",
        name: "Quản lý bài viết",
      },
      {
        id: 'admin-categories',
        href: "/admin/blog/categories",
        name: "Chỉnh sửa danh mục",
      },
      {
        id: 'admin-tags',
        href: "/admin/blog/tags",
        name: "Chỉnh sửa thẻ",
      },
    ],
  },
  {
    id: 'admin-system',
    href: "",
    name: "<PERSON>à<PERSON> đặt",
    type: "dropdown",
    children: [
      {
        id: 'admin-settings',
        href: "/admin/settings",
        name: "<PERSON><PERSON><PERSON> đặt hệ thống",
      },
      {
        id: 'admin-theme',
        href: "/admin/theme",
        name: "<PERSON>iao diện",
      },
      {
        id: 'admin-users',
        href: "/admin/users",
        name: "Người dùng",
      },
      {
        id: 'admin-backup',
        href: "/admin/backup",
        name: "Sao lưu",
      },
    ],
  },
];

/**
 * Demo component để test NavigationAdmin với fixed fade out → fade in animation
 */
const NavigationAdminFadeFixed: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* NavigationAdmin với fixed fade animations */}
      <NavigationAdmin
        menu={TEST_NAVIGATION_ADMIN}
        isAdmin={true}
        postId={123}
      />
      
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            NavigationAdmin - Fixed Fade Out → Fade In
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Submenu với animation fade out → fade in đã được fix và hoạt động đúng
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ✅ Fixed Animation Features
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Proper Triggering</strong>: Animation starts on hover</li>
                <li>• <strong>Fade Out → In</strong>: Multi-stage opacity transition</li>
                <li>• <strong>Blur Effects</strong>: blur(3px) → blur(0) progression</li>
                <li>• <strong>Scale Transform</strong>: scale(0.9) → scale(1)</li>
                <li>• <strong>Smooth Movement</strong>: translateY(25px) → translateY(0)</li>
                <li>• <strong>Staggered Items</strong>: Individual animation names</li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🔧 Technical Fixes Applied
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Animation Names</strong>: Unique names cho mỗi item</li>
                <li>• <strong>Fill Mode</strong>: forwards để maintain final state</li>
                <li>• <strong>Enhanced Keyframes</strong>: More detailed progression</li>
                <li>• <strong>Proper Delays</strong>: Staggered timing</li>
                <li>• <strong>Visibility Control</strong>: Proper show/hide states</li>
                <li>• <strong>Pointer Events</strong>: Interaction management</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Animation Timeline */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-900 dark:text-blue-100">
            ⏱️ Fixed Animation Timeline
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Container Animation (800ms)
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex justify-between">
                  <span>0-120ms (15%):</span>
                  <span>Initial fade out với blur(3px)</span>
                </div>
                <div className="flex justify-between">
                  <span>120-280ms (35%):</span>
                  <span>Gradual fade với blur(2px)</span>
                </div>
                <div className="flex justify-between">
                  <span>280-400ms (50%):</span>
                  <span>Mid-point với blur(1.5px)</span>
                </div>
                <div className="flex justify-between">
                  <span>400-560ms (70%):</span>
                  <span>Fade in với blur(1px)</span>
                </div>
                <div className="flex justify-between">
                  <span>560-800ms (100%):</span>
                  <span>Final fade in với blur(0)</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Staggered Items (600ms each)
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex justify-between">
                  <span>200ms:</span>
                  <span>Item 1 starts fading in</span>
                </div>
                <div className="flex justify-between">
                  <span>300ms:</span>
                  <span>Item 2 starts fading in</span>
                </div>
                <div className="flex justify-between">
                  <span>400ms:</span>
                  <span>Item 3 starts fading in</span>
                </div>
                <div className="flex justify-between">
                  <span>500ms:</span>
                  <span>Item 4 starts fading in</span>
                </div>
                <div className="flex justify-between">
                  <span>1300ms:</span>
                  <span>All animations complete</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
            🧪 Test Fixed Animation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Fade Out → In Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Hover "Menu quản lý" → Watch fade out → fade in</li>
                <li>2. Observe blur effect progression</li>
                <li>3. Notice scale transformation</li>
                <li>4. Watch smooth movement</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Staggered Animation Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Test "Cài đặt" (4 items) → Sequential appearance</li>
                <li>2. Rapid hover/unhover → Smooth interruption</li>
                <li>3. Multiple hovers → Consistent behavior</li>
                <li>4. Different menus → Same quality</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Before/After Fix */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-red-900 dark:text-red-100">
              ❌ Before Fix (Issues)
            </h3>
            <ul className="space-y-2 text-sm text-red-800 dark:text-red-200">
              <li>• Animation không trigger khi hover</li>
              <li>• Keyframes không được execute</li>
              <li>• Submenu xuất hiện instant</li>
              <li>• Không có fade out → fade in effect</li>
              <li>• Staggered animation không hoạt động</li>
              <li>• Animation fill-mode issues</li>
            </ul>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
              ✅ After Fix (Working)
            </h3>
            <ul className="space-y-2 text-sm text-green-800 dark:text-green-200">
              <li>• Animation triggers properly on hover</li>
              <li>• Keyframes execute smoothly</li>
              <li>• Clear fade out → fade in sequence</li>
              <li>• Beautiful blur và scale effects</li>
              <li>• Staggered items với unique names</li>
              <li>• Proper animation fill-mode</li>
            </ul>
          </div>
        </div>

        {/* Demo Content */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Technical Implementation Details
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Các fixes chính đã được áp dụng để đảm bảo animation hoạt động:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Key Fixes</h4>
              <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Unique Animation Names</strong>: menuItemFadeIn1, menuItemFadeIn2, etc.</li>
                <li>• <strong>Animation Fill Mode</strong>: forwards để maintain final state</li>
                <li>• <strong>Enhanced Keyframes</strong>: More detailed progression steps</li>
                <li>• <strong>Proper Visibility</strong>: visibility: visible on hover</li>
                <li>• <strong>Pointer Events</strong>: auto để enable interaction</li>
                <li>• <strong>Longer Duration</strong>: 800ms cho container, 600ms cho items</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Animation Properties</h4>
              <pre className="text-xs bg-gray-200 dark:bg-gray-700 p-3 rounded overflow-x-auto">
{`&:hover .sub-menu {
  visibility: visible !important;
  pointer-events: auto !important;
  animation: submenuFadeOutIn 0.8s 
    cubic-bezier(0.4, 0, 0.2, 1) 
    forwards !important;
  animation-fill-mode: forwards !important;
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationAdminFadeFixed;
