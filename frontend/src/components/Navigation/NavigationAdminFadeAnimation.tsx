'use client';

import React from 'react';
import NavigationAdmin from './NavigationAdmin';
import { INavItemType } from '@/contains/types';

// Test data với dropdown menu để test fade animation
const TEST_NAVIGATION_ADMIN: INavItemType[] = [
  {
    id: 'admin-home',
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: 'admin-content',
    href: "",
    name: "<PERSON><PERSON> quản lý",
    type: "dropdown",
    children: [
      {
        id: 'admin-posts',
        href: "/admin/blog/posts",
        name: "Quản lý bài viết",
      },
      {
        id: 'admin-categories',
        href: "/admin/blog/categories",
        name: "Chỉnh sửa danh mục",
      },
      {
        id: 'admin-tags',
        href: "/admin/blog/tags",
        name: "Chỉnh sửa thẻ",
      },
    ],
  },
  {
    id: 'admin-system',
    href: "",
    name: "<PERSON>ài đặt",
    type: "dropdown",
    children: [
      {
        id: 'admin-settings',
        href: "/admin/settings",
        name: "<PERSON>à<PERSON> đặt hệ thống",
      },
      {
        id: 'admin-theme',
        href: "/admin/theme",
        name: "<PERSON><PERSON>o diện",
      },
      {
        id: 'admin-users',
        href: "/admin/users",
        name: "Người dùng",
      },
      {
        id: 'admin-backup',
        href: "/admin/backup",
        name: "Sao lưu",
      },
    ],
  },
];

/**
 * Demo component để test NavigationAdmin với fade animations
 */
const NavigationAdminFadeAnimation: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* NavigationAdmin với fade animations */}
      <NavigationAdmin
        menu={TEST_NAVIGATION_ADMIN}
        isAdmin={true}
        postId={123}
      />
      
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            NavigationAdmin - Smooth Fade Animations
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Submenu với animation fade mượt mà và dần hiện ra rõ ràng
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ✨ Fade In Animation
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Initial</strong>: opacity: 0, translateY(15px)</li>
                <li>• <strong>Hover</strong>: opacity: 1, translateY(0)</li>
                <li>• <strong>Duration</strong>: 0.3s với smooth ease-out</li>
                <li>• <strong>Stagger</strong>: Menu items fade in với 50ms delay</li>
                <li>• <strong>Background</strong>: Solid white/dark với shadow</li>
                <li>• <strong>Visibility</strong>: Proper visibility management</li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🎯 Visual Improvements
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Clear Background</strong>: Solid colors, no transparency</li>
                <li>• <strong>Proper Shadows</strong>: Enhanced depth perception</li>
                <li>• <strong>Rounded Corners</strong>: Modern 12px border radius</li>
                <li>• <strong>High Contrast</strong>: Optimized text colors</li>
                <li>• <strong>Smooth Hover</strong>: Background transitions</li>
                <li>• <strong>Dark Mode</strong>: Proper dark theme support</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Animation Details */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-900 dark:text-blue-100">
            🔧 Animation Technical Details
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Fade In Sequence
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex justify-between">
                  <span>0ms:</span>
                  <span>Submenu starts fading in</span>
                </div>
                <div className="flex justify-between">
                  <span>50ms:</span>
                  <span>First menu item appears</span>
                </div>
                <div className="flex justify-between">
                  <span>100ms:</span>
                  <span>Second menu item appears</span>
                </div>
                <div className="flex justify-between">
                  <span>150ms:</span>
                  <span>Third menu item appears</span>
                </div>
                <div className="flex justify-between">
                  <span>300ms:</span>
                  <span>Animation complete</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                CSS Properties
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex justify-between">
                  <span>Opacity:</span>
                  <span>0 → 1 (smooth fade)</span>
                </div>
                <div className="flex justify-between">
                  <span>Transform:</span>
                  <span>translateY(15px) → translateY(0)</span>
                </div>
                <div className="flex justify-between">
                  <span>Visibility:</span>
                  <span>hidden → visible</span>
                </div>
                <div className="flex justify-between">
                  <span>Pointer Events:</span>
                  <span>none → auto</span>
                </div>
                <div className="flex justify-between">
                  <span>Transition:</span>
                  <span>0.3s ease-out</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
            🧪 Test Fade Animation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Basic Fade Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Hover "Menu quản lý" → Watch smooth fade in</li>
                <li>2. Observe staggered menu items</li>
                <li>3. Move mouse away → Watch fade out</li>
                <li>4. Test rapid hover → No flicker</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Visual Quality Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Check background opacity → Should be solid</li>
                <li>2. Test text readability → High contrast</li>
                <li>3. Hover menu items → Smooth background change</li>
                <li>4. Toggle dark mode → Proper theme adaptation</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Before/After Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-red-900 dark:text-red-100">
              ❌ Before (Issues)
            </h3>
            <ul className="space-y-2 text-sm text-red-800 dark:text-red-200">
              <li>• Submenu bị mờ và khó đọc</li>
              <li>• Background không rõ ràng</li>
              <li>• Animation quá nhanh hoặc jerky</li>
              <li>• Text contrast thấp</li>
              <li>• Flicker khi rapid hover</li>
              <li>• Không có staggered animation</li>
            </ul>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
              ✅ After (Fixed)
            </h3>
            <ul className="space-y-2 text-sm text-green-800 dark:text-green-200">
              <li>• Submenu rõ ràng và dễ đọc</li>
              <li>• Background solid với proper shadows</li>
              <li>• Smooth 0.3s fade animation</li>
              <li>• High contrast text colors</li>
              <li>• Stable hover behavior</li>
              <li>• Beautiful staggered menu items</li>
            </ul>
          </div>
        </div>

        {/* Demo Content */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Animation Implementation Details
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Animation được thiết kế để tạo cảm giác mượt mà và professional:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Key Features</h4>
              <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                <li>• Smooth opacity transition (0 → 1)</li>
                <li>• Subtle slide up (15px distance)</li>
                <li>• Staggered menu items (50ms delay)</li>
                <li>• Proper visibility management</li>
                <li>• Pointer events control</li>
                <li>• Enhanced visual styling</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">CSS Implementation</h4>
              <pre className="text-xs bg-gray-200 dark:bg-gray-700 p-3 rounded overflow-x-auto">
{`.sub-menu {
  opacity: 0;
  transform: translateY(15px);
  transition: opacity 0.3s ease-out,
              transform 0.3s ease-out,
              visibility 0.3s ease-out;
  visibility: hidden;
  pointer-events: none;
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationAdminFadeAnimation;
