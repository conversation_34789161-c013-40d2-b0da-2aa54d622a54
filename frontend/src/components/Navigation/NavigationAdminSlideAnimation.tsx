'use client';

import React from 'react';
import NavigationAdmin from './NavigationAdmin';
import { INavItemType } from '@/contains/types';

// Test data với nhiều dropdown menu để test slide animation
const TEST_NAVIGATION_ADMIN: INavItemType[] = [
  {
    id: 'admin-home',
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: 'admin-content',
    href: "",
    name: "Quản lý nội dung",
    type: "dropdown",
    children: [
      {
        id: 'admin-posts',
        href: "/admin/blog/posts",
        name: "<PERSON><PERSON><PERSON> viết",
      },
      {
        id: 'admin-pages',
        href: "/admin/pages",
        name: "Trang tĩnh",
      },
      {
        id: 'admin-categories',
        href: "/admin/blog/categories",
        name: "<PERSON><PERSON> mụ<PERSON>",
      },
      {
        id: 'admin-tags',
        href: "/admin/blog/tags",
        name: "Thẻ",
      },
      {
        id: 'admin-media',
        href: "/admin/media",
        name: "Media",
      },
      {
        id: 'admin-comments',
        href: "/admin/comments",
        name: "<PERSON><PERSON><PERSON> luận",
      },
    ],
  },
  {
    id: 'admin-users',
    href: "",
    name: "Người dùng",
    type: "dropdown",
    children: [
      {
        id: 'admin-all-users',
        href: "/admin/users",
        name: "Tất cả người dùng",
      },
      {
        id: 'admin-roles',
        href: "/admin/roles",
        name: "Vai trò",
      },
      {
        id: 'admin-permissions',
        href: "/admin/permissions",
        name: "Phân quyền",
      },
      {
        id: 'admin-profile',
        href: "/admin/profile",
        name: "Hồ sơ",
      },
    ],
  },
  {
    id: 'admin-system',
    href: "",
    name: "Hệ thống",
    type: "dropdown",
    children: [
      {
        id: 'admin-settings',
        href: "/admin/settings",
        name: "Cài đặt",
      },
      {
        id: 'admin-theme',
        href: "/admin/theme",
        name: "Giao diện",
      },
      {
        id: 'admin-backup',
        href: "/admin/backup",
        name: "Sao lưu",
      },
    ],
  },
];

/**
 * Demo component để test NavigationAdmin với slide animations
 */
const NavigationAdminSlideAnimation: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* NavigationAdmin với slide animations */}
      <NavigationAdmin
        menu={TEST_NAVIGATION_ADMIN}
        isAdmin={true}
        postId={123}
      />
      
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            NavigationAdmin - Slide Up/Down Animations
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Submenu với animation trượt từ dưới lên khi mở và trượt từ trên xuống khi đóng
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                📈 Slide Up Animation (Open)
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Initial State</strong>: opacity: 0, translateY(10px)</li>
                <li>• <strong>Hover State</strong>: opacity: 1, translateY(0)</li>
                <li>• <strong>Duration</strong>: 0.25s với cubic-bezier easing</li>
                <li>• <strong>Staggered Items</strong>: 50ms delay giữa các items</li>
                <li>• <strong>Effect</strong>: Submenu trượt từ dưới lên mượt mà</li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                📉 Slide Down Animation (Close)
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Exit State</strong>: opacity: 0, translateY(10px)</li>
                <li>• <strong>No Hover</strong>: visibility: hidden</li>
                <li>• <strong>Duration</strong>: 0.25s với smooth transition</li>
                <li>• <strong>Instant Hide</strong>: No delay cho closing</li>
                <li>• <strong>Effect</strong>: Submenu trượt xuống và fade out</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Animation Timeline */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-900 dark:text-blue-100">
            ⏱️ Animation Timeline
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Open Animation Sequence
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex justify-between">
                  <span>0ms:</span>
                  <span>Submenu container starts sliding up</span>
                </div>
                <div className="flex justify-between">
                  <span>50ms:</span>
                  <span>First menu item fades in</span>
                </div>
                <div className="flex justify-between">
                  <span>100ms:</span>
                  <span>Second menu item fades in</span>
                </div>
                <div className="flex justify-between">
                  <span>150ms:</span>
                  <span>Third menu item fades in</span>
                </div>
                <div className="flex justify-between">
                  <span>250ms:</span>
                  <span>Animation complete</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Close Animation Sequence
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex justify-between">
                  <span>0ms:</span>
                  <span>All items start fading out</span>
                </div>
                <div className="flex justify-between">
                  <span>0ms:</span>
                  <span>Container starts sliding down</span>
                </div>
                <div className="flex justify-between">
                  <span>250ms:</span>
                  <span>Submenu completely hidden</span>
                </div>
                <div className="flex justify-between">
                  <span>250ms:</span>
                  <span>Visibility set to hidden</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
            🧪 Test Instructions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Basic Animation Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Hover "Quản lý nội dung" → Watch slide up animation</li>
                <li>2. Move mouse away → Watch slide down animation</li>
                <li>3. Hover "Người dùng" → Test staggered item animation</li>
                <li>4. Rapid hover/unhover → Test animation smoothness</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Advanced Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Test với toolbar slide animations</li>
                <li>2. Test trong collapsed/expanded states</li>
                <li>3. Test responsive behavior</li>
                <li>4. Test dark mode transitions</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Animation Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-red-900 dark:text-red-100">
              ❌ Before (No Animation)
            </h4>
            <div className="space-y-2 text-sm text-red-800 dark:text-red-200">
              <p>• Instant appearance/disappearance</p>
              <p>• No visual feedback</p>
              <p>• Jarring user experience</p>
              <p>• No transition smoothness</p>
            </div>
          </div>
          
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-yellow-900 dark:text-yellow-100">
              ⚠️ Headless UI Default
            </h4>
            <div className="space-y-2 text-sm text-yellow-800 dark:text-yellow-200">
              <p>• Fade + translateY(1px)</p>
              <p>• Too subtle movement</p>
              <p>• 150ms duration</p>
              <p>• No staggered items</p>
            </div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-green-900 dark:text-green-100">
              ✅ Custom Slide Animation
            </h4>
            <div className="space-y-2 text-sm text-green-800 dark:text-green-200">
              <p>• Slide up từ 10px distance</p>
              <p>• Smooth cubic-bezier easing</p>
              <p>• 250ms optimal duration</p>
              <p>• Staggered menu items</p>
            </div>
          </div>
        </div>

        {/* Demo Content */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Demo Content Area
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Animation được thiết kế để tạo cảm giác natural và responsive:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Animation Features</h4>
              <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                <li>• Slide up từ dưới lên (10px distance)</li>
                <li>• Cubic-bezier easing cho smoothness</li>
                <li>• Staggered animation cho menu items</li>
                <li>• Instant close để avoid delay</li>
                <li>• Optimized duration (250ms)</li>
                <li>• GPU-accelerated transforms</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Technical Details</h4>
              <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                <li>• CSS transitions thay vì keyframes</li>
                <li>• Transform + opacity cho performance</li>
                <li>• Visibility management cho accessibility</li>
                <li>• Override Headless UI defaults</li>
                <li>• Responsive timing adjustments</li>
                <li>• Dark mode compatible</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationAdminSlideAnimation;
