'use client';

import React from 'react';
import NavigationAdmin from './NavigationAdmin';
import { INavItemType } from '@/contains/types';

// Test data với dropdown menu để test advanced fade animation
const TEST_NAVIGATION_ADMIN: INavItemType[] = [
  {
    id: 'admin-home',
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: 'admin-content',
    href: "",
    name: "<PERSON><PERSON> quản lý",
    type: "dropdown",
    children: [
      {
        id: 'admin-posts',
        href: "/admin/blog/posts",
        name: "Quản lý bài viết",
      },
      {
        id: 'admin-categories',
        href: "/admin/blog/categories",
        name: "Chỉnh sửa danh mục",
      },
      {
        id: 'admin-tags',
        href: "/admin/blog/tags",
        name: "Chỉnh sửa thẻ",
      },
      {
        id: 'admin-media',
        href: "/admin/media",
        name: "Quản lý media",
      },
    ],
  },
  {
    id: 'admin-system',
    href: "",
    name: "<PERSON><PERSON><PERSON> đặt hệ thống",
    type: "dropdown",
    children: [
      {
        id: 'admin-settings',
        href: "/admin/settings",
        name: "Cài đặt chung",
      },
      {
        id: 'admin-theme',
        href: "/admin/theme",
        name: "Giao diện",
      },
      {
        id: 'admin-users',
        href: "/admin/users",
        name: "Người dùng",
      },
      {
        id: 'admin-backup',
        href: "/admin/backup",
        name: "Sao lưu dữ liệu",
      },
      {
        id: 'admin-logs',
        href: "/admin/logs",
        name: "Nhật ký hệ thống",
      },
    ],
  },
  {
    id: 'admin-reports',
    href: "",
    name: "Báo cáo",
    type: "dropdown",
    children: [
      {
        id: 'admin-analytics',
        href: "/admin/analytics",
        name: "Thống kê truy cập",
      },
      {
        id: 'admin-performance',
        href: "/admin/performance",
        name: "Hiệu suất",
      },
    ],
  },
];

/**
 * Demo component để test NavigationAdmin với advanced fade animations
 */
const NavigationAdminAdvancedFade: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* NavigationAdmin với advanced fade animations */}
      <NavigationAdmin
        menu={TEST_NAVIGATION_ADMIN}
        isAdmin={true}
        postId={123}
      />
      
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            NavigationAdmin - Advanced Fade Out → Fade In
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Submenu với animation mờ dần rồi hiện ra với hiệu ứng blur và scale
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🌟 Advanced Animation Effects
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Multi-stage</strong>: Fade out → Fade in sequence</li>
                <li>• <strong>Blur Effect</strong>: blur(2px) → blur(0) transition</li>
                <li>• <strong>Scale Transform</strong>: scale(0.95) → scale(1)</li>
                <li>• <strong>Smooth Movement</strong>: translateY(20px) → translateY(0)</li>
                <li>• <strong>Cubic Bezier</strong>: Professional easing curves</li>
                <li>• <strong>Staggered Items</strong>: Sequential menu item appearance</li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ⏱️ Animation Timeline
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>0-200ms</strong>: Container fade out → fade in</li>
                <li>• <strong>200ms</strong>: First menu item starts appearing</li>
                <li>• <strong>250ms</strong>: Second menu item appears</li>
                <li>• <strong>300ms</strong>: Third menu item appears</li>
                <li>• <strong>350ms</strong>: Fourth menu item appears</li>
                <li>• <strong>600ms</strong>: Animation sequence complete</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Animation Breakdown */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-900 dark:text-blue-100">
            🎬 Animation Breakdown
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Stage 1: Fade Out (0-30%)
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <p>• Opacity: 0 → 0.3</p>
                <p>• Transform: translateY(20px) scale(0.95)</p>
                <p>• Filter: blur(2px) → blur(1px)</p>
                <p>• Effect: Mờ dần và blur</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Stage 2: Transition (30-60%)
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <p>• Opacity: 0.3 → 0.7</p>
                <p>• Transform: translateY(10px) scale(0.98)</p>
                <p>• Filter: blur(1px) → blur(0.5px)</p>
                <p>• Effect: Dần rõ ràng hơn</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Stage 3: Fade In (60-100%)
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <p>• Opacity: 0.7 → 1</p>
                <p>• Transform: translateY(5px) → translateY(0)</p>
                <p>• Filter: blur(0.5px) → blur(0)</p>
                <p>• Effect: Hoàn toàn rõ ràng</p>
              </div>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
            🧪 Test Advanced Animation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Multi-Stage Effect Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Hover "Menu quản lý" → Watch fade out → fade in</li>
                <li>2. Observe blur effect during transition</li>
                <li>3. Notice scale transformation</li>
                <li>4. Watch staggered menu items</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Advanced Features Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Test "Cài đặt hệ thống" (5 items) → Long stagger</li>
                <li>2. Test "Báo cáo" (2 items) → Quick sequence</li>
                <li>3. Rapid hover → Smooth interruption</li>
                <li>4. Mobile test → Touch-friendly timing</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Animation Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-red-900 dark:text-red-100">
              ❌ Basic Fade
            </h4>
            <div className="space-y-2 text-sm text-red-800 dark:text-red-200">
              <p>• Simple opacity transition</p>
              <p>• No blur effects</p>
              <p>• Linear animation</p>
              <p>• Instant appearance</p>
            </div>
          </div>
          
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-yellow-900 dark:text-yellow-100">
              ⚠️ Standard Slide
            </h4>
            <div className="space-y-2 text-sm text-yellow-800 dark:text-yellow-200">
              <p>• Simple slide up/down</p>
              <p>• Basic opacity change</p>
              <p>• Single-stage animation</p>
              <p>• No visual effects</p>
            </div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
            <h4 className="font-semibold mb-3 text-green-900 dark:text-green-100">
              ✅ Advanced Fade
            </h4>
            <div className="space-y-2 text-sm text-green-800 dark:text-green-200">
              <p>• Multi-stage fade sequence</p>
              <p>• Blur + scale effects</p>
              <p>• Cubic-bezier easing</p>
              <p>• Professional appearance</p>
            </div>
          </div>
        </div>

        {/* Demo Content */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Technical Implementation
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Animation sử dụng CSS keyframes với multi-stage effects:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Keyframe Animation</h4>
              <pre className="text-xs bg-gray-200 dark:bg-gray-700 p-3 rounded overflow-x-auto">
{`@keyframes submenuFadeOutIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    filter: blur(2px);
  }
  30% {
    opacity: 0.3;
    transform: translateY(10px) scale(0.98);
    filter: blur(1px);
  }
  60% {
    opacity: 0.7;
    transform: translateY(5px) scale(0.99);
    filter: blur(0.5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}`}
              </pre>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Animation Properties</h4>
              <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Duration</strong>: 0.6s cho container</li>
                <li>• <strong>Easing</strong>: cubic-bezier(0.4, 0, 0.2, 1)</li>
                <li>• <strong>Effects</strong>: opacity + transform + filter</li>
                <li>• <strong>Stagger</strong>: 50ms delay giữa items</li>
                <li>• <strong>Performance</strong>: GPU-accelerated</li>
                <li>• <strong>Fallback</strong>: Graceful degradation</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationAdminAdvancedFade;
