'use client';

import React from 'react';
import NavigationAdmin from './NavigationAdmin';
import { INavItemType } from '@/contains/types';

// Test data với dropdown menu
const TEST_NAVIGATION_ADMIN: INavItemType[] = [
  {
    id: 'admin-home',
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: 'admin-menu',
    href: "",
    name: "<PERSON>u quản lý",
    type: "dropdown",
    children: [
      {
        id: 'admin-posts',
        href: "/admin/blog/posts",
        name: "Quản lý bài viết",
      },
      {
        id: 'admin-categories',
        href: "/admin/blog/categories",
        name: "Chỉnh sửa danh mục",
      },
      {
        id: 'admin-tags',
        href: "/admin/blog/tags",
        name: "Chỉnh sửa thẻ",
      },
    ],
  },
  {
    id: 'admin-settings',
    href: "",
    name: "Cài đặt",
    type: "dropdown",
    children: [
      {
        id: 'admin-theme',
        href: "/admin/theme",
        name: "<PERSON><PERSON><PERSON>",
      },
      {
        id: 'admin-users',
        href: "/admin/users",
        name: "Người dùng",
      },
    ],
  },
];

/**
 * Demo component cho NavigationAdmin với slide up/down animations
 */
const NavigationAdminSlideDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* NavigationAdmin với slide animations */}
      <NavigationAdmin
        menu={TEST_NAVIGATION_ADMIN}
        isAdmin={true}
        postId={123}
      />
      
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            NavigationAdmin với Slide Up/Down Animations
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Toolbar có animation trượt xuống khi hiển thị và trượt lên khi ẩn
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                📥 Slide Down (Show)
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Trigger</strong>: Click sticky button "Hiển thị menu"</li>
                <li>• <strong>Animation</strong>: slideDownFromTop với bounce effect</li>
                <li>• <strong>Duration</strong>: 0.6s với cubic-bezier easing</li>
                <li>• <strong>Sequence</strong>: Toolbar → Button → Menu → Items</li>
                <li>• <strong>Stagger</strong>: Menu items với 50ms delay</li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                📤 Slide Up (Hide)
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Trigger</strong>: Click "Ẩn menu admin"</li>
                <li>• <strong>Animation</strong>: slideUpToTop với smooth exit</li>
                <li>• <strong>Duration</strong>: 0.5s với ease-in timing</li>
                <li>• <strong>Sequence</strong>: Items → Menu → Button → Toolbar</li>
                <li>• <strong>Reverse Stagger</strong>: Items fade out first</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Animation Timeline */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-900 dark:text-blue-100">
            ⏱️ Animation Timeline
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Show Animation (Slide Down)
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex justify-between">
                  <span>0.0s - 0.6s:</span>
                  <span>Toolbar slide down</span>
                </div>
                <div className="flex justify-between">
                  <span>0.2s - 0.7s:</span>
                  <span>Toggle button scale in</span>
                </div>
                <div className="flex justify-between">
                  <span>0.3s - 0.8s:</span>
                  <span>Menu container fade up</span>
                </div>
                <div className="flex justify-between">
                  <span>0.4s - 0.8s:</span>
                  <span>Menu items staggered</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">
                Hide Animation (Slide Up)
              </h4>
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex justify-between">
                  <span>0.0s - 0.3s:</span>
                  <span>Menu items fade out</span>
                </div>
                <div className="flex justify-between">
                  <span>0.05s - 0.45s:</span>
                  <span>Menu container fade down</span>
                </div>
                <div className="flex justify-between">
                  <span>0.1s - 0.4s:</span>
                  <span>Toggle button scale out</span>
                </div>
                <div className="flex justify-between">
                  <span>0.0s - 0.5s:</span>
                  <span>Toolbar slide up</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
            🧪 Test Instructions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Basic Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Click "Ẩn menu admin" → Watch slide up animation</li>
                <li>2. Click sticky button → Watch slide down animation</li>
                <li>3. Repeat để test consistency</li>
                <li>4. Test hover effects trên menu items</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">
                Advanced Test
              </h4>
              <ol className="space-y-1 text-sm text-green-700 dark:text-green-300">
                <li>1. Test rapid clicking (animation queuing)</li>
                <li>2. Resize browser (responsive animations)</li>
                <li>3. Test với reduced motion preference</li>
                <li>4. Test submenu functionality during animations</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Demo Content */}
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Demo Content Area
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Animations được thiết kế để tạo cảm giác natural và responsive. 
              Slide down có bounce effect để tạo sự sinh động, 
              slide up có timing mượt mà để không gây khó chịu.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <div key={item} className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    Feature {item}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Technical Details */}
          <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
              🔧 Technical Implementation
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Key Features</h4>
                <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                  <li>• State management với isHiding flag</li>
                  <li>• Conditional rendering với animation classes</li>
                  <li>• Timeout để sync animation với state</li>
                  <li>• Reverse staggered animations</li>
                  <li>• Responsive timing adjustments</li>
                  <li>• Accessibility với reduced motion</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Performance</h4>
                <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                  <li>• GPU-accelerated transforms</li>
                  <li>• Optimized animation timing</li>
                  <li>• Minimal DOM manipulation</li>
                  <li>• Efficient CSS keyframes</li>
                  <li>• Mobile-optimized durations</li>
                  <li>• Smooth 60fps animations</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationAdminSlideDemo;
