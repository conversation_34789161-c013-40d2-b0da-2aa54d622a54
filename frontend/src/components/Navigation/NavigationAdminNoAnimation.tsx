'use client';

import React from 'react';
import NavigationAdmin from './NavigationAdmin';
import { INavItemType } from '@/contains/types';

// Test data với nhiều dropdown menu để test no-animation
const TEST_NAVIGATION_ADMIN: INavItemType[] = [
  {
    id: 'admin-home',
    href: "/admin",
    name: "Trang admin",
  },
  {
    id: 'admin-content',
    href: "",
    name: "Quản lý nội dung",
    type: "dropdown",
    children: [
      {
        id: 'admin-posts',
        href: "/admin/blog/posts",
        name: "<PERSON><PERSON><PERSON> viết",
      },
      {
        id: 'admin-pages',
        href: "/admin/pages",
        name: "<PERSON>rang tĩnh",
      },
      {
        id: 'admin-categories',
        href: "/admin/blog/categories",
        name: "<PERSON><PERSON> mục",
      },
      {
        id: 'admin-tags',
        href: "/admin/blog/tags",
        name: "Thẻ",
      },
      {
        id: 'admin-media',
        href: "/admin/media",
        name: "Media",
      },
    ],
  },
  {
    id: 'admin-users',
    href: "",
    name: "<PERSON><PERSON><PERSON><PERSON> dùng",
    type: "dropdown",
    children: [
      {
        id: 'admin-all-users',
        href: "/admin/users",
        name: "Tất cả người dùng",
      },
      {
        id: 'admin-roles',
        href: "/admin/roles",
        name: "Vai trò",
      },
      {
        id: 'admin-permissions',
        href: "/admin/permissions",
        name: "Phân quyền",
      },
    ],
  },
  {
    id: 'admin-system',
    href: "",
    name: "Hệ thống",
    type: "dropdown",
    children: [
      {
        id: 'admin-settings',
        href: "/admin/settings",
        name: "Cài đặt",
      },
      {
        id: 'admin-theme',
        href: "/admin/theme",
        name: "Giao diện",
      },
      {
        id: 'admin-backup',
        href: "/admin/backup",
        name: "Sao lưu",
      },
      {
        id: 'admin-logs',
        href: "/admin/logs",
        name: "Nhật ký",
      },
    ],
  },
];

/**
 * Demo component để test NavigationAdmin với no animations
 */
const NavigationAdminNoAnimation: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* NavigationAdmin với no animations */}
      <NavigationAdmin
        menu={TEST_NAVIGATION_ADMIN}
        isAdmin={true}
        postId={123}
      />
      
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            NavigationAdmin - No Submenu Animations
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Submenu hiển thị ngay lập tức không có animation nào
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                🚫 Animations Removed
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Transition</strong>: transition: none !important</li>
                <li>• <strong>Animation</strong>: animation: none !important</li>
                <li>• <strong>Transform</strong>: transform: none !important</li>
                <li>• <strong>Opacity</strong>: opacity: 1 !important</li>
                <li>• <strong>Headless UI</strong>: Override tất cả Transition classes</li>
                <li>• <strong>Instant Display</strong>: Submenu xuất hiện ngay lập tức</li>
              </ul>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                ⚡ Performance Benefits
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li>• <strong>Instant Response</strong>: No animation delays</li>
                <li>• <strong>Better UX</strong>: Immediate feedback</li>
                <li>• <strong>No Flicker</strong>: Stable submenu display</li>
                <li>• <strong>CPU Efficient</strong>: No animation calculations</li>
                <li>• <strong>Accessibility</strong>: Better for motion sensitivity</li>
                <li>• <strong>Consistent</strong>: Works with all animations</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
            🧪 Test No-Animation Behavior
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-blue-800 dark:text-blue-200">
                Instant Display Test
              </h4>
              <ol className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                <li>1. Hover "Quản lý nội dung" → Submenu xuất hiện ngay</li>
                <li>2. Hover "Người dùng" → No fade/slide animation</li>
                <li>3. Hover "Hệ thống" → Instant visibility</li>
                <li>4. Rapid hover → No animation queuing</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 text-blue-800 dark:text-blue-200">
                Animation Override Test
              </h4>
              <ol className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                <li>1. Test với slide animations → Submenu không bị ảnh hưởng</li>
                <li>2. Collapse/expand → Submenu vẫn instant</li>
                <li>3. Dark mode toggle → No animation interference</li>
                <li>4. Mobile responsive → Consistent behavior</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Before/After Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-red-900 dark:text-red-100">
              ❌ Before (With Animations)
            </h3>
            <ul className="space-y-2 text-sm text-red-800 dark:text-red-200">
              <li>• Submenu fade in với 150ms delay</li>
              <li>• Transform translateY animation</li>
              <li>• Opacity transition từ 0 → 1</li>
              <li>• Animation queuing issues</li>
              <li>• Flicker khi rapid hover</li>
              <li>• Submenu bị mờ trong animation</li>
            </ul>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
              ✅ After (No Animations)
            </h3>
            <ul className="space-y-2 text-sm text-green-800 dark:text-green-200">
              <li>• Submenu xuất hiện ngay lập tức</li>
              <li>• No transform effects</li>
              <li>• Opacity luôn = 1</li>
              <li>• No animation conflicts</li>
              <li>• Stable hover behavior</li>
              <li>• Clear, readable submenu</li>
            </ul>
          </div>
        </div>

        {/* Technical Implementation */}
        <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg mb-8">
          <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
            🔧 Technical Implementation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">CSS Overrides</h4>
              <pre className="text-xs bg-gray-200 dark:bg-gray-700 p-3 rounded overflow-x-auto">
{`.nc-NavigationAdmin-container
  .menu-item.menu-dropdown
  .sub-menu {
    transition: none !important;
    animation: none !important;
    transform: none !important;
    opacity: 1 !important;
  }`}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-gray-900 dark:text-white">Headless UI Override</h4>
              <pre className="text-xs bg-gray-200 dark:bg-gray-700 p-3 rounded overflow-x-auto">
{`[data-headlessui-state],
[class*="transition"],
[class*="opacity-0"] {
  transition: none !important;
  opacity: 1 !important;
}`}
              </pre>
            </div>
          </div>
        </div>

        {/* Demo Content */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Demo Content Area
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Submenu giờ đây hiển thị ngay lập tức không có animation nào. 
            Điều này cải thiện UX và performance, đặc biệt tốt cho:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h4 className="font-medium mb-2 text-blue-900 dark:text-blue-100">
                Accessibility
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                Tốt cho người dùng có motion sensitivity hoặc vestibular disorders
              </p>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h4 className="font-medium mb-2 text-green-900 dark:text-green-100">
                Performance
              </h4>
              <p className="text-sm text-green-800 dark:text-green-200">
                Giảm CPU usage và battery consumption trên mobile devices
              </p>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <h4 className="font-medium mb-2 text-purple-900 dark:text-purple-100">
                User Experience
              </h4>
              <p className="text-sm text-purple-800 dark:text-purple-200">
                Instant feedback và no animation delays cho power users
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationAdminNoAnimation;
